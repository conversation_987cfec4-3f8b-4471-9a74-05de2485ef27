"use client";

import React, { useState, useEffect, useRef } from "react";
import Wrapper from "@/components/Wrapper";
import Marquee from "react-fast-marquee";

const logos = [
  "/img1.jpg",
  "/img2.png",
  "/img3.webp",
  "/img4.webp",
  "/img5.webp",
  "/img6.webp",
  "/img7.webp",
  "/img8.jpg",
];

const AnimatedCounter = ({ target, duration = 2000 }) => {
  const [count, setCount] = useState(0);
  const [hasAnimated, setHasAnimated] = useState(false);
  const counterRef = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasAnimated) {
          setHasAnimated(true);

          let startTime;
          let animationId;

          const animate = (currentTime) => {
            if (!startTime) startTime = currentTime;
            const progress = Math.min((currentTime - startTime) / duration, 1);

            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            setCount(Math.floor(easeOutQuart * target));

            if (progress < 1) {
              animationId = requestAnimationFrame(animate);
            }
          };

          animationId = requestAnimationFrame(animate);

          return () => {
            if (animationId) {
              cancelAnimationFrame(animationId);
            }
          };
        }
      },
      { threshold: 0.3 }
    );

    if (counterRef.current) {
      observer.observe(counterRef.current);
    }

    return () => observer.disconnect();
  }, [target, duration, hasAnimated]);

  return <span ref={counterRef} className="font-bold text-blue-600">{count}</span>;
};

const TrustedBy = () => (
  <Wrapper padding="default" background="transparent">
    <section className="w-full flex flex-col items-center py-8 md:py-12">
      <h4 className="text-gray-600 font-bold mb-8 md:mb-12 text-center text-lg md:text-xl lg:text-2xl tracking-wider uppercase">
        TRUSTED BY MORE THAN <AnimatedCounter target={100} />+ COMPANIES WORLDWIDE
      </h4>

      <div className="w-full overflow-hidden bg-gradient-to-r from-gray-50 via-white to-gray-50 rounded-2xl shadow-inner">
        <Marquee
          gradient={true}
          gradientColor="white"
          gradientWidth={100}
          speed={40}
          pauseOnHover={true}
          className="py-8 md:py-12"
        >
          {[...logos, ...logos].map((logo, idx) => (
            <div key={idx} className="mx-6 md:mx-10 lg:mx-12 flex items-center justify-center group">
              <div className="relative p-4 md:p-6 bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100">
                <img
                  src={logo}
                  alt={`Trusted company logo ${idx + 1}`}
                  className="h-8 md:h-12 lg:h-14 object-contain transition-all duration-500 group-hover:scale-110 filter brightness-90 group-hover:brightness-100"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-transparent via-transparent to-white opacity-0 group-hover:opacity-20 rounded-xl transition-opacity duration-500"></div>
              </div>
            </div>
          ))}
        </Marquee>

        {/* Second row with reverse direction for more dynamic effect */}
        <Marquee
          gradient={true}
          gradientColor="white"
          gradientWidth={100}
          speed={35}
          direction="right"
          pauseOnHover={true}
          className="py-4 md:py-6 -mt-4"
        >
          {[...logos.slice().reverse(), ...logos.slice().reverse()].map((logo, idx) => (
            <div key={`reverse-${idx}`} className="mx-6 md:mx-10 lg:mx-12 flex items-center justify-center group">
              <div className="relative p-3 md:p-4 bg-gradient-to-br from-white to-gray-50 rounded-lg shadow-md hover:shadow-lg transition-all duration-400 transform hover:scale-105 border border-gray-50">
                <img
                  src={logo}
                  alt={`Trusted company logo ${idx + 1}`}
                  className="h-6 md:h-8 lg:h-10 object-contain transition-all duration-400 group-hover:scale-105 opacity-80 group-hover:opacity-100"
                />
              </div>
            </div>
          ))}
        </Marquee>
      </div>
    </section>
  </Wrapper>
);

export default TrustedBy;
